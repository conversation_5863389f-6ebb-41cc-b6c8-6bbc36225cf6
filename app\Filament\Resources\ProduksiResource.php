<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProduksiResource\Pages;
use App\Models\Produk;
use App\Models\Produksi;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ProduksiResource extends Resource
{
    protected static ?string $model = Produksi::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $modelLabel = 'Produksi';
    protected static ?string $pluralModelLabel = 'Produksi';
    protected static ?string $slug = 'produksi';
    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([ // Defines the form fields for creating/editing records
                Select::make('produk_id')
                    ->relationship('produk', 'nama_produk') // Load options from 'produk' relationship, display 'nama_produk'
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label('Nama Produk'),
                TextInput::make('jumlah')
                    ->required()
                    ->numeric() // Ensure input is numeric
                    ->minValue(1) // Minimum allowed value
                    ->label('Jumlah'),
                DatePicker::make('tanggal')
                    ->required()
                    ->label('Tanggal Produksi')
                    ->default(now()) // Set default value to current date
                    ->displayFormat('d F Y') // How the date is displayed
                    ->native(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([ // Defines the columns displayed in the table
                TextColumn::make('produk.nama_produk') // Display 'nama_produk' from the 'produk' relationship
                    ->searchable()
                    ->sortable()
                    ->label('Nama Produk'),
                TextColumn::make('jumlah')
                    ->sortable()
                    ->label('Jumlah'),
                TextColumn::make('tanggal')
                    ->date('d F Y') // Format the date display
                    ->sortable()
                    ->label('Tanggal Produksi')
                    ->searchable(query: function (\Illuminate\Database\Eloquent\Builder $query, string $search): \Illuminate\Database\Eloquent\Builder {
                        return $query->where(function (\Illuminate\Database\Eloquent\Builder $subQuery) use ($search) {
                            // Original English month/year search
                            $subQuery->orWhereRaw("DATE_FORMAT(tanggal, '%d %M %Y') LIKE ?", ['%' . $search . '%'])
                                     ->orWhereRaw("DATE_FORMAT(tanggal, '%d %b %Y') LIKE ?", ['%' . $search . '%'])
                                     ->orWhere('tanggal', 'like', '%' . $search . '%');

                            // Indonesian month search logic
                            $indonesianMonths = [
                                'januari' => 1, 'februari' => 2, 'maret' => 3, 'april' => 4,
                                'mei' => 5, 'juni' => 6, 'juli' => 7, 'agustus' => 8,
                                'september' => 9, 'oktober' => 10, 'november' => 11, 'desember' => 12,
                            ];
                            $searchLower = strtolower(trim($search));
                            $parts = preg_split('/\s+/', $searchLower, -1, PREG_SPLIT_NO_EMPTY);

                            $dayNumber = null;
                            $monthNumber = null;
                            $yearNumber = null;

                            // Attempt to parse dd Month YYYY, Month YYYY, dd Month, Month, YYYY
                            if (count($parts) === 1) {
                                if (is_numeric($parts[0])) {
                                    if (strlen($parts[0]) === 4) $yearNumber = $parts[0]; // YYYY
                                    // else if (strlen($parts[0]) <= 2) $dayNumber = $parts[0]; // dd - less likely to be useful alone
                                } else {
                                    $monthNumber = $indonesianMonths[$parts[0]] ?? null; // Month
                                }
                            } elseif (count($parts) === 2) {
                                // Month YYYY
                                if (isset($indonesianMonths[$parts[0]]) && is_numeric($parts[1]) && strlen($parts[1]) === 4) {
                                    $monthNumber = $indonesianMonths[$parts[0]];
                                    $yearNumber = $parts[1];
                                }
                                // dd Month
                                elseif (is_numeric($parts[0]) && strlen($parts[0]) <=2 && isset($indonesianMonths[$parts[1]])) {
                                   $dayNumber = $parts[0];
                                   $monthNumber = $indonesianMonths[$parts[1]];
                                }
                            } elseif (count($parts) === 3) {
                                // dd Month YYYY
                                if (is_numeric($parts[0]) && strlen($parts[0]) <= 2 && isset($indonesianMonths[$parts[1]]) && is_numeric($parts[2]) && strlen($parts[2]) === 4) {
                                    $dayNumber = $parts[0];
                                    $monthNumber = $indonesianMonths[$parts[1]];
                                    $yearNumber = $parts[2];
                                }
                            }

                            if ($dayNumber && $monthNumber && $yearNumber) {
                                $subQuery->orWhere(function (\Illuminate\Database\Eloquent\Builder $q) use ($dayNumber, $monthNumber, $yearNumber) {
                                    $q->whereDay('tanggal', $dayNumber)
                                      ->whereMonth('tanggal', $monthNumber)
                                      ->whereYear('tanggal', $yearNumber);
                                });
                            } elseif ($monthNumber && $yearNumber) {
                                $subQuery->orWhere(function (\Illuminate\Database\Eloquent\Builder $q) use ($monthNumber, $yearNumber) {
                                    $q->whereMonth('tanggal', $monthNumber)
                                      ->whereYear('tanggal', $yearNumber);
                                });
                            } elseif ($dayNumber && $monthNumber) {
                                $subQuery->orWhere(function (\Illuminate\Database\Eloquent\Builder $q) use ($dayNumber, $monthNumber) {
                                    $q->whereDay('tanggal', $dayNumber)
                                      ->whereMonth('tanggal', $monthNumber);
                                });
                            } elseif ($monthNumber) {
                                $subQuery->orWhereMonth('tanggal', $monthNumber);
                            } elseif ($yearNumber) {
                                $subQuery->orWhereYear('tanggal', $yearNumber);
                            }
                        });
                    }),
            ])
            ->filters([ // Defines the filters available for the table
                SelectFilter::make('produk_id') // Filter by product
                    ->label('Nama Produk')
                    ->relationship('produk', 'nama_produk') // Use relationship for filter options
                    ->searchable()
                    ->preload(),
                Filter::make('tanggal') // Custom filter for date range
                    ->form([ // Define the filter's input fields
                        DatePicker::make('tanggal_dari')
                            ->label('Dari Tanggal')
                            ->displayFormat('d F Y')
                            ->native(false),
                        DatePicker::make('tanggal_sampai')
                            ->label('Sampai Tanggal')
                            ->displayFormat('d F Y')
                            ->native(false),
                    ])
                    ->query(function (Builder $query, array $data): Builder { // Apply the filter logic to the query
                        return $query
                            ->when(
                                $data['tanggal_dari'], // If 'tanggal_dari' is set
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal', '>=', $date), // Filter records on or after this date
                            )
                            ->when(
                                $data['tanggal_sampai'], // If 'tanggal_sampai' is set
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal', '<=', $date), // Filter records on or before this date
                            );
                    })
            ])
            ->actions([ // Defines actions available for each row
                EditAction::make()
                    ->hidden(),
                DeleteAction::make() // Row action to delete a record
                    ->label('Hapus')
                    ->modalHeading('Hapus Data Produksi')
                    ->modalDescription('Apakah Anda yakin ingin menghapus data produksi ini?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->modalCancelActionLabel('Batal'),
            ])
            ->bulkActions([ // Defines actions available when multiple rows are selected
                BulkActionGroup::make([
                    DeleteBulkAction::make() // Bulk action to delete selected records
                        ->label('Hapus yang dipilih')
                        ->modalHeading('Hapus Data Produksi')
                        ->modalDescription('Apakah Anda yakin ingin menghapus data produksi yang dipilih?')
                        ->modalSubmitActionLabel('Ya, Hapus')
                        ->modalCancelActionLabel('Batal'),
                ]),
            ])
            ->defaultSort('tanggal', 'desc'); // Default sorting order for the table
    }

    public static function getPages(): array // Defines the pages associated with this resource
    {
        return [
            'index' => Pages\ListProduksis::route('/'), // Route for the list page
            'create' => Pages\CreateProduksi::route('/create'), // Route for the create page
            'edit' => Pages\EditProduksi::route('/{record}/edit'), // Route for the edit page
        ];
    }
}